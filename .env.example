# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/appraisal_db"

# Authentication (Clerk)
CLERK_SECRET_KEY="sk_test_your_secret_key_here"
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_your_publishable_key_here"

# Clerk URLs (optional - Clerk will use defaults if not set)
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"

# NextAuth Secret (32+ characters)
NEXTAUTH_SECRET="your-32-character-secret-key-here-change-this"

# Application Environment
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="60000"

# Logging
LOG_LEVEL="info"
ENABLE_CONSOLE_LOGGING="true"
ENABLE_DEBUG_LOGS="true"

# Security
CSRF_SECRET="your-csrf-secret-here"
SESSION_SECRET="your-session-secret-here"

# Optional: External Services
# SENTRY_DSN="your-sentry-dsn-here"
# ANALYTICS_ID="your-analytics-id-here"
