"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  DollarSign, 
  Minus, 
  Plus, 
  Calculator, 
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageSquare
} from "lucide-react"
import { toast } from "sonner"
import type { SalaryAdjustmentType, CommentType, CommentPriority } from "@/lib/types"

const accountingNotesSchema = z.object({
  notes: z.string().min(10, "Notes must be at least 10 characters").max(1000, "Notes must be less than 1000 characters"),
  adjustmentAmount: z.number().optional(),
  adjustmentType: z.enum(['deduction', 'bonus', 'none']).optional(),
  adjustmentReason: z.string().optional(),
  commentType: z.enum(['general', 'deduction', 'bonus', 'correction', 'payment_issue']).default('general'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal')
})

type AccountingNotesFormValues = z.infer<typeof accountingNotesSchema>

interface SalaryDeductionManagerProps {
  appraisalId: string
  employeeName: string
  currentAdjustment?: {
    amount: number | null
    type: SalaryAdjustmentType | null
    reason: string | null
  }
  onSave?: () => void
}

const adjustmentTypeOptions = [
  { value: 'none', label: 'No Adjustment', icon: Calculator, color: 'text-gray-600', bgColor: 'bg-gray-100' },
  { value: 'deduction', label: 'Deduction', icon: Minus, color: 'text-red-600', bgColor: 'bg-red-100' },
  { value: 'bonus', label: 'Bonus', icon: Plus, color: 'text-green-600', bgColor: 'bg-green-100' }
] as const

const commentTypeOptions = [
  { value: 'general', label: 'General Note' },
  { value: 'deduction', label: 'Deduction Note' },
  { value: 'bonus', label: 'Bonus Note' },
  { value: 'correction', label: 'Correction' },
  { value: 'payment_issue', label: 'Payment Issue' }
] as const

const priorityOptions = [
  { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: 'normal', label: 'Normal', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
] as const

export function SalaryDeductionManager({ 
  appraisalId, 
  employeeName, 
  currentAdjustment,
  onSave 
}: SalaryDeductionManagerProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<AccountingNotesFormValues>({
    resolver: zodResolver(accountingNotesSchema),
    defaultValues: {
      notes: '',
      adjustmentAmount: currentAdjustment?.amount || undefined,
      adjustmentType: currentAdjustment?.type || 'none',
      adjustmentReason: currentAdjustment?.reason || '',
      commentType: 'general',
      priority: 'normal'
    }
  })

  const selectedAdjustmentType = form.watch('adjustmentType')
  const adjustmentAmount = form.watch('adjustmentAmount')
  const selectedPriority = form.watch('priority')

  const selectedTypeOption = adjustmentTypeOptions.find(opt => opt.value === selectedAdjustmentType)
  const selectedPriorityOption = priorityOptions.find(opt => opt.value === selectedPriority)

  async function onSubmit(data: AccountingNotesFormValues) {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/accounting/notes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appraisalId,
          notes: data.notes,
          adjustmentAmount: data.adjustmentType !== 'none' ? data.adjustmentAmount : null,
          adjustmentType: data.adjustmentType !== 'none' ? data.adjustmentType : null,
          adjustmentReason: data.adjustmentType !== 'none' ? data.adjustmentReason : null,
          commentType: data.commentType,
          priority: data.priority
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Accounting notes saved successfully!')
        form.reset()
        onSave?.()
      } else {
        toast.error(result.error || 'Failed to save accounting notes')
      }
    } catch (error) {
      console.error('Error saving accounting notes:', error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Accounting Review
        </CardTitle>
        <CardDescription>
          Add accounting notes and salary adjustments for <strong>{employeeName}</strong>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Current Adjustment Display */}
            {currentAdjustment && (currentAdjustment.amount || currentAdjustment.type !== 'none') && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Current Adjustment</h4>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-blue-700">Type:</span>
                    <Badge variant="outline" className={
                      currentAdjustment.type === 'deduction' ? 'bg-red-100 text-red-800' :
                      currentAdjustment.type === 'bonus' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {currentAdjustment.type || 'None'}
                    </Badge>
                  </div>
                  {currentAdjustment.amount && (
                    <div className="flex items-center gap-2">
                      <span className="text-blue-700">Amount:</span>
                      <span className="font-medium">${Math.abs(currentAdjustment.amount).toFixed(2)}</span>
                    </div>
                  )}
                </div>
                {currentAdjustment.reason && (
                  <p className="text-sm text-blue-700 mt-2">
                    <strong>Reason:</strong> {currentAdjustment.reason}
                  </p>
                )}
              </div>
            )}

            {/* Adjustment Type Selection */}
            <FormField
              control={form.control}
              name="adjustmentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Salary Adjustment Type</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-3 gap-3">
                      {adjustmentTypeOptions.map((option) => {
                        const Icon = option.icon
                        const isSelected = field.value === option.value
                        return (
                          <div
                            key={option.value}
                            className={`p-3 border rounded-lg cursor-pointer transition-all ${
                              isSelected 
                                ? 'border-primary bg-primary/5 ring-2 ring-primary/20' 
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => field.onChange(option.value)}
                          >
                            <div className="flex items-center gap-2 mb-1">
                              <div className={`w-8 h-8 rounded-full ${option.bgColor} flex items-center justify-center`}>
                                <Icon className={`h-4 w-4 ${option.color}`} />
                              </div>
                              <span className="font-medium text-sm">{option.label}</span>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Adjustment Amount and Reason (only if not 'none') */}
            {selectedAdjustmentType && selectedAdjustmentType !== 'none' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="adjustmentAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {selectedAdjustmentType === 'deduction' ? 'Deduction' : 'Bonus'} Amount
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input 
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            className="pl-10"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Enter the {selectedAdjustmentType} amount in dollars
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="adjustmentReason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for {selectedAdjustmentType}</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder={`Reason for ${selectedAdjustmentType}...`}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Brief explanation for the adjustment
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Separator />

            {/* Comment Type and Priority */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="commentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comment Type</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {commentTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {priorityOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className={option.color}>
                                  {option.label}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Accounting Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Accounting Notes</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field} 
                      placeholder="Add detailed accounting notes, payment instructions, or any special considerations..."
                      rows={4}
                      maxLength={1000}
                    />
                  </FormControl>
                  <FormDescription>
                    {field.value.length}/1000 characters
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Summary */}
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Review Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <span>Adjustment:</span>
                  {selectedTypeOption && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <selectedTypeOption.icon className={`h-3 w-3 ${selectedTypeOption.color}`} />
                      {selectedTypeOption.label}
                      {adjustmentAmount && selectedAdjustmentType !== 'none' && (
                        <span className="ml-1">${adjustmentAmount.toFixed(2)}</span>
                      )}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span>Priority:</span>
                  {selectedPriorityOption && (
                    <Badge variant="outline" className={selectedPriorityOption.color}>
                      {selectedPriorityOption.label}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full"
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Save Accounting Review
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
