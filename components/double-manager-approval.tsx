"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  CheckCircle, 
  Clock, 
  Users, 
  UserCheck, 
  AlertCircle,
  Eye,
  MessageSquare
} from "lucide-react"
import type { ApprovalWorkflow, ApprovalStep } from "@/lib/types"

interface DoubleManagerApprovalProps {
  workflow: ApprovalWorkflow
  steps: ApprovalStep[]
  onViewAppraisal?: (appraisalId: string) => void
}

const stepStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  skipped: 'bg-gray-100 text-gray-800'
}

const workflowStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  in_progress: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800'
}

export function DoubleManagerApproval({ workflow, steps, onViewAppraisal }: DoubleManagerApprovalProps) {
  const [progress, setProgress] = useState(0)

  // Calculate progress
  useEffect(() => {
    const totalSteps = steps.length
    const approvedSteps = steps.filter(step => step.status === 'approved').length
    const progressPercentage = totalSteps > 0 ? (approvedSteps / totalSteps) * 100 : 0
    setProgress(progressPercentage)
  }, [steps])

  // Group steps by level
  const stepsByLevel = steps.reduce((acc, step) => {
    if (!acc[step.stepLevel]) {
      acc[step.stepLevel] = []
    }
    acc[step.stepLevel].push(step)
    return acc
  }, {} as Record<number, ApprovalStep[]>)

  const isDualManagerWorkflow = workflow.workflowType === 'dual_manager'
  const level1Steps = stepsByLevel[1] || []
  const level2Steps = stepsByLevel[2] || []

  // Check if all level 1 steps are approved
  const level1Complete = level1Steps.length > 0 && level1Steps.every(step => step.status === 'approved')
  const hasRejectedSteps = steps.some(step => step.status === 'rejected')

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {isDualManagerWorkflow ? (
                <Users className="h-5 w-5" />
              ) : (
                <UserCheck className="h-5 w-5" />
              )}
              {isDualManagerWorkflow ? 'Dual Manager Approval' : 'Manager Cascade Approval'}
            </CardTitle>
            <CardDescription>
              {isDualManagerWorkflow 
                ? 'Requires approval from all assigned managers before proceeding to next level'
                : 'Sequential approval through management hierarchy'
              }
            </CardDescription>
          </div>
          <Badge 
            variant="outline" 
            className={workflowStatusColors[workflow.status]}
          >
            {workflow.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Approval Progress</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        {/* Level 1: Manager Approvals */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              level1Complete ? 'bg-green-100 text-green-700' : 
              workflow.currentLevel === 1 ? 'bg-blue-100 text-blue-700' : 
              'bg-gray-100 text-gray-600'
            }`}>
              {level1Complete ? <CheckCircle className="h-4 w-4" /> : '1'}
            </div>
            <h3 className="font-medium">
              Level 1: {isDualManagerWorkflow ? 'All Managers Must Approve' : 'Direct Manager Approval'}
            </h3>
            {workflow.currentLevel === 1 && !level1Complete && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                Current
              </Badge>
            )}
          </div>

          <div className="ml-10 space-y-3">
            {level1Steps.map((step) => (
              <div key={step.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    step.status === 'approved' ? 'bg-green-100' :
                    step.status === 'rejected' ? 'bg-red-100' :
                    'bg-yellow-100'
                  }`}>
                    {step.status === 'approved' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : step.status === 'rejected' ? (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{step.approverName || 'Manager'}</p>
                    <p className="text-sm text-muted-foreground capitalize">{step.approverRole}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    className={stepStatusColors[step.status]}
                  >
                    {step.status}
                  </Badge>
                  {step.approvedAt && (
                    <span className="text-xs text-muted-foreground">
                      {new Date(step.approvedAt).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            ))}

            {isDualManagerWorkflow && level1Steps.length > 1 && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800">
                  <Users className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    All {level1Steps.length} managers must approve before proceeding to Level 2
                  </span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  {level1Steps.filter(s => s.status === 'approved').length} of {level1Steps.length} approved
                </p>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Level 2: Senior Manager Approval */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              level2Steps.some(s => s.status === 'approved') ? 'bg-green-100 text-green-700' : 
              workflow.currentLevel === 2 ? 'bg-blue-100 text-blue-700' : 
              'bg-gray-100 text-gray-600'
            }`}>
              {level2Steps.some(s => s.status === 'approved') ? <CheckCircle className="h-4 w-4" /> : '2'}
            </div>
            <h3 className="font-medium">Level 2: Senior Management Approval</h3>
            {workflow.currentLevel === 2 && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                Current
              </Badge>
            )}
          </div>

          <div className="ml-10 space-y-3">
            {level2Steps.length > 0 ? (
              level2Steps.map((step) => (
                <div key={step.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      step.status === 'approved' ? 'bg-green-100' :
                      step.status === 'rejected' ? 'bg-red-100' :
                      workflow.currentLevel === 2 ? 'bg-yellow-100' : 'bg-gray-100'
                    }`}>
                      {step.status === 'approved' ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : step.status === 'rejected' ? (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      ) : workflow.currentLevel === 2 ? (
                        <Clock className="h-4 w-4 text-yellow-600" />
                      ) : (
                        <Clock className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{step.approverName || 'Senior Manager'}</p>
                      <p className="text-sm text-muted-foreground capitalize">{step.approverRole}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant="outline" 
                      className={stepStatusColors[step.status]}
                    >
                      {step.status}
                    </Badge>
                    {step.approvedAt && (
                      <span className="text-xs text-muted-foreground">
                        {new Date(step.approvedAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Waiting for Level 1 completion before Level 2 becomes available
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Comments Section */}
        {steps.some(step => step.comments || step.rejectionReason) && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Comments & Feedback
              </h4>
              {steps
                .filter(step => step.comments || step.rejectionReason)
                .map((step) => (
                  <div key={step.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium text-sm">{step.approverName}</span>
                      <Badge variant="outline" size="sm">
                        Level {step.stepLevel}
                      </Badge>
                    </div>
                    {step.rejectionReason && (
                      <div className="mb-2">
                        <p className="text-sm font-medium text-red-700">Rejection Reason:</p>
                        <p className="text-sm text-red-600">{step.rejectionReason}</p>
                      </div>
                    )}
                    {step.comments && (
                      <div>
                        <p className="text-sm font-medium">Comments:</p>
                        <p className="text-sm text-muted-foreground">{step.comments}</p>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </>
        )}

        {/* Actions */}
        {onViewAppraisal && (
          <div className="flex justify-end">
            <Button 
              variant="outline" 
              onClick={() => onViewAppraisal(workflow.appraisalId)}
            >
              <Eye className="h-4 w-4 mr-2" />
              View Full Appraisal
            </Button>
          </div>
        )}

        {/* Status Summary */}
        <div className="p-4 bg-muted rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Workflow Status</p>
              <p className="text-sm text-muted-foreground">
                {workflow.status === 'completed' ? 'All approvals completed successfully' :
                 workflow.status === 'rejected' ? 'Workflow rejected - requires resubmission' :
                 workflow.status === 'in_progress' ? `Currently at Level ${workflow.currentLevel}` :
                 'Waiting for initial approval'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">
                Level {workflow.currentLevel} of {workflow.totalLevels}
              </p>
              {workflow.completedAt && (
                <p className="text-xs text-muted-foreground">
                  Completed: {new Date(workflow.completedAt).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
