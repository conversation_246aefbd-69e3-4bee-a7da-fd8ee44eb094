import { getCurrentUser } from "@/lib/auth"
import { getPendingFeedbackForHR, getFeedbackStatistics } from "@/lib/data/feedback"
import { HRFeedbackDashboard } from "@/components/hr-feedback-dashboard"
import { redirect } from "next/navigation"

export default async function HRFeedbackPage() {
  const currentUser = await getCurrentUser()
  
  if (!currentUser) {
    redirect('/sign-in')
  }

  // Check if user has HR permissions
  if (!['hr-admin', 'super-admin'].includes(currentUser.role)) {
    redirect('/dashboard')
  }

  // Fetch feedback data
  const [pendingFeedback, statistics] = await Promise.all([
    getPendingFeedbackForHR(),
    getFeedbackStatistics()
  ])

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">HR Feedback Management</h1>
        <p className="text-muted-foreground">
          Review and manage employee feedback submissions
        </p>
      </div>

      <HRFeedbackDashboard 
        pendingFeedback={pendingFeedback}
        statistics={statistics}
      />
    </div>
  )
}
