import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { getFeedbackById, updateFeedbackStatus, addFeedbackComment } from '@/lib/data/feedback'
import { z } from 'zod'

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'under_review', 'investigating', 'resolved', 'closed', 'escalated']),
  hrResponse: z.string().optional(),
  hrNotes: z.string().optional()
})

const addCommentSchema = z.object({
  comment: z.string().min(1).max(1000),
  isInternal: z.boolean().default(true)
})

/**
 * GET - Get feedback details by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to view feedback (HR/Admin only)
    if (!['hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const feedback = await getFeedbackById(id)
    
    if (!feedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 })
    }

    return NextResponse.json({ 
      success: true, 
      feedback 
    })

  } catch (error) {
    console.error('Error fetching feedback:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch feedback' 
    }, { status: 500 })
  }
}

/**
 * PATCH - Update feedback status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to update feedback (HR/Admin only)
    if (!['hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateStatusSchema.parse(body)

    const result = await updateFeedbackStatus(
      id,
      validatedData.status,
      currentUser.id,
      validatedData.hrResponse,
      validatedData.hrNotes
    )

    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Feedback status updated successfully' 
      })
    } else {
      return NextResponse.json({ 
        error: result.error || 'Failed to update feedback status' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error updating feedback status:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid input data',
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Failed to update feedback status' 
    }, { status: 500 })
  }
}

/**
 * POST - Add comment to feedback
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to comment (HR/Admin only)
    if (!['hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = addCommentSchema.parse(body)

    const result = await addFeedbackComment(
      id,
      currentUser.id,
      validatedData.comment,
      validatedData.isInternal
    )

    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Comment added successfully' 
      })
    } else {
      return NextResponse.json({ 
        error: result.error || 'Failed to add comment' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error adding comment:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid input data',
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Failed to add comment' 
    }, { status: 500 })
  }
}
