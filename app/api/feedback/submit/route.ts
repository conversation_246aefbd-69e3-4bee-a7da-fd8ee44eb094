import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { submitFeedback } from '@/lib/data/feedback'
import { z } from 'zod'

const submitFeedbackSchema = z.object({
  targetEmployeeId: z.string().uuid().optional().nullable(),
  feedbackType: z.enum(['complaint', 'suggestion', 'recognition', 'concern', 'initiative', 'general']),
  category: z.enum(['performance', 'behavior', 'communication', 'teamwork', 'leadership', 'process', 'other']).optional().nullable(),
  subject: z.string().min(5).max(255),
  message: z.string().min(20).max(2000),
  isAnonymous: z.boolean().default(false),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium')
})

/**
 * POST - Submit new employee feedback
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the employee record for the current user
    const { supabaseAdmin } = await import('@/lib/supabase')
    const { data: employee, error: employeeError } = await supabaseAdmin
      .from('appy_employees')
      .select('id')
      .eq('manager_id', currentUser.id) // Assuming manager_id maps to Clerk user ID
      .single()

    // If not found as manager, try to find as employee by email or other identifier
    let employeeId = employee?.id
    if (!employeeId) {
      // Try to find employee by email or create mapping
      // For now, we'll use the Clerk user ID directly
      employeeId = currentUser.id
    }

    const body = await request.json()
    
    // Validate input
    const validatedData = submitFeedbackSchema.parse(body)

    // Submit feedback
    const result = await submitFeedback(employeeId, {
      targetEmployeeId: validatedData.targetEmployeeId,
      feedbackType: validatedData.feedbackType,
      category: validatedData.category,
      subject: validatedData.subject,
      message: validatedData.message,
      isAnonymous: validatedData.isAnonymous,
      priority: validatedData.priority
    })

    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        feedbackId: result.feedbackId,
        message: 'Feedback submitted successfully' 
      })
    } else {
      return NextResponse.json({ 
        error: result.error || 'Failed to submit feedback' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error in feedback submission:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid input data',
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Failed to submit feedback' 
    }, { status: 500 })
  }
}
